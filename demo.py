"""
文檔可參考
https://ai.pydantic.dev/models/gemini/#gemini-via-generative-language-api
"""

import os

from pydantic import BaseModel, Field
from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel

# 1. 設置 Gemini API Key 環境變數
#    根據文檔，GeminiModel 會自動從 GEMINI_API_KEY 環境變數讀取
os.environ['GEMINI_API_KEY'] = "AIzaSyCaQqcOHichiXAAqbjprGVKwXWg0sHcEa4"

# 2. 定義你想要的最終資料結構 (Pydantic 模型)
class FilmCriticReview(BaseModel):
    film_name: str = Field(description="被評論的電影名稱")
    rating: int = Field(description="從 1 到 10 的評分", ge=1, le=10)
    critique: str = Field(description="一句話的簡短評論")

# 3. 準備非結構化的原始文字
unstructured_review = """
我昨天去看了《沙丘：第二部》，整體來說，視覺效果絕對是頂級的，
給它打 9 分應該不為過。這部電影完美地擴展了第一部的世界觀，
真的是一場史詩級的觀影體驗。
"""

# 4. 建立一個 PydanticAI 專用的 Gemini 模型
#    根據文檔，需要使用 GeminiModel 並指定 provider
model = GeminiModel(
    'gemini-2.0-flash-exp',  # 模型名稱
    provider='google-gla'    # 使用 Google Generative Language API
)

# 5. 建立 Agent，並將 Gemini 模型傳入
#    使用新的 output_type 參數而不是已棄用的 result_type
agent = Agent(model, output_type=FilmCriticReview)

# 6. 執行解析！
#    使用 agent.run_sync() 方法來執行同步調用
result = agent.run_sync(f"從以下影評中提取結構化資訊: '{unstructured_review}'")

# 7. 得到一個乾淨、結構化、可用的 Python 物件！
#    result.output 包含解析後的結構化資料
review_object = result.output
print(review_object)
# 可能的輸出: film_name='沙丘：第二部' rating=9 critique='這部電影完美地擴展了第一部的世界觀，真的是一場史詩級的觀影體驗。'

print(f"\n電影名稱: {review_object.film_name}")
print(f"評分: {review_object.rating}/10")
print(f"評論: {review_object.critique}")

